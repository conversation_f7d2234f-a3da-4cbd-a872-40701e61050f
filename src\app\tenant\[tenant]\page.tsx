import fs from "fs";
import path from "path";

async function loadTenantData(tenant: string) {
  try {
    const filePath = path.join(process.cwd(), "data", "tenants", `${tenant}.json`);
    const json = await fs.promises.readFile(filePath, "utf8");
    return JSON.parse(json);
  } catch {
    return null;
  }
}

interface TenantPageProps {
  params: Promise<{ tenant: string }>;
}

export default async function TenantPage({ params }: TenantPageProps) {
  const { tenant } = await params; // важно! теперь ждём params
  const data = await loadTenantData(tenant);

  if (!data) {
    return (
      <div style={{ padding: 20 }}>
        <h1>Нет данных для {tenant}</h1>
        <p>Создайте файл data/tenants/{tenant}.json</p>
      </div>
    );
  }

  return (
    <div style={{ padding: 20 }}>
      <h1>{data.title}</h1>
      {data.slides.map((slide: any) => (
        <div key={slide.index} style={{ marginBottom: 20 }}>
          <h2>{slide.title}</h2>
          {slide.url && (
            <img
              src={slide.url}
              alt={slide.title}
              width={400}
              style={{ display: "block", marginBottom: 10 }}
            />
          )}
          <p>{slide.presentation_text}</p>
        </div>
      ))}
    </div>
  );
}
