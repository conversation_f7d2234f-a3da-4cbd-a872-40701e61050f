[{"name":"BCA Complex workflow with 4 mini-agents","active":true,"nodes":[{"parameters":{"public":true,"initialMessages":"","options":{}},"type":"@n8n/n8n-nodes-langchain.chatTrigger","typeVersion":1.1,"position":[-864,0],"id":"ba6d41f1-c035-4804-b9b1-ccc8bc68f860","name":"Message_From_BCA_Agent","webhookId":"4bbaf3fb-bdde-48a4-89ba-5efb23909218"},{"parameters":{"mode":"raw","jsonOutput":"{\\n  \\"relevant_links\\": [\\n    {\\n      \\"url\\": \\"https://www.gesetze-im-internet.de/aufenthg_2004/\\",\\n      \\"title\\": \\"Закон о пребывании, трудоустройстве и интеграции иностранцев в Германии (Aufenthaltsgesetz – AufenthG). Этот закон регулирует вопросы въезда, пребывания, трудоустройства и интеграции иностранцев в Германии.\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.bamf.de/\\",\\n      \\"title\\": \\"BAMF – Федеральное ведомство по миграции и беженцам. Центральный орган, отвечающий за миграционную политику, интеграцию и предоставление убежища в Германии.\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.make-it-in-germany.com/\\",\\n      \\"title\\": \\"Make it in Germany – Портал для квалифицированных специалистов. Официальный портал, предоставляющий информацию о работе, визах и жизни в Германии для иностранных специалистов.\\"\\n    }\\n    ,\\n    {\\n      \\"url\\": \\"https://anabin.kmk.org/\\",\\n      \\"title\\": \\"Anabin – Признание иностранных дипломов. База данных для проверки эквивалентности иностранных образовательных квалификаций в Германии.\\"\\n    },\\n    {\\n      \\"url\\": \\"https://jobboerse.arbeitsagentur.de/\\",\\n      \\"title\\": \\"Jobbörse der Bundesagentur für Arbeit – Поиск работы. Официальный портал Федерального агентства по труду для поиска вакансий в Германии.\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.netzwerk-iq.de/\\",\\n      \\"title\\": \\"IQ Netzwerk – Интеграция через квалификацию. Сеть, поддерживающая интеграцию мигрантов через признание квалификаций и профессиональное развитие.\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.arbeitsagentur.de/\\",\\n      \\"title\\": \\"ZAV – Центральное ведомство по трудоустройству иностранцев. Центральный орган, отвечающий за миграционную политику, интеграцию и предоставление убежища в Германии.\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.berlin.de/einwanderung/\\",\\n      \\"title\\": \\"Ссылка на сайт миграционного ведомства (Ausländerbehörde) в городе Германии: Берлин\\"\\n    },\\n    {\\n      \\"url\\": \\"https://stadt.muenchen.de/buergerservice/ausland-migration.html\\",\\n      \\"title\\": \\"Ссылка на сайт миграционного ведомства (Ausländerbehörde) в городе Германии: Мюнхен\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.hamburg.de/service/\\",\\n      \\"title\\": \\"Ссылка на сайт миграционного ведомства (Ausländerbehörde) в городе Германии: Гамбург\\"\\n    },\\n    {\\n      \\"url\\": \\"https://frankfurt.de/service-und-rathaus/verwaltung/aemter-und-institutionen/ordnungsamt/auslaenderbehoerde\\",\\n      \\"title\\": \\"Ссылка на сайт миграционного ведомства (Ausländerbehörde) в городе Германии: Франкфурт-на-Майне\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.stadt-koeln.de/\\",\\n      \\"title\\": \\"Ссылка на сайт миграционного ведомства (Ausländerbehörde) в городе Германии: Кёльн\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.stuttgart.de/service/dienstleistungen/\\",\\n      \\"title\\": \\"Ссылка на сайт миграционного ведомства (Ausländerbehörde) в городе Германии: Штутгарт\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.duesseldorf.de/auslaenderamt\\",\\n      \\"title\\": \\"Ссылка на сайт миграционного ведомства (Ausländerbehörde) в городе Германии: Дюссельдорф\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.leipzig.de/\\",\\n      \\"title\\": \\"Ссылка на сайт миграционного ведомства (Ausländerbehörde) в городе Германии: Лейпциг\\"\\n    },\\n    {\\n      \\"url\\": \\"https://www.dortmund.de/suche/\\",\\n      \\"title\\": \\"Ссылка на сайт миграционного ведомства (Ausländerbehörde) в городе Германии: Дортмунд\\"\\n    }\\n  ]\\n}\\n","options":{}},"type":"n8n-nodes-base.set","typeVersion":3.4,"position":[-592,0],"id":"58fb118c-6641-4881-b9c8-43b5317b9be9","name":"Relevant_Links"},{"parameters":{"promptType":"define","text":"={{ $('Message_From_BCA_Agent').item.json.chatInput }}","hasOutputParser":true,"options":{"systemMessage":"=1. Получи пользовательский запрос.\\n2. Найди информацию по вопросу пользователя, используя только локальную базу документов (knowledge base RAG). Если информации нет или она не релевантна - не заполняй поле \\"data_from_knowladge_base\\".\\n3. По запросу пользователя, если требуется найти релевантные нормативные документы или законы, используй такой шаблон для поиск ссылок в Scrapeless: site:example.com [запрос пользователя]. Получи список ссылок с этого сайта, затем выбери релевантные, передай их на дальнейший скрепинг в переменную \\"scraped_links_for_crawl\\". Используй для поиска по сайтам только ниже приведенные или полученные из базы знаний документов (knowledge base RAG). Если результата по ссылкам нет — \\"scraped_links_for_crawl\\" должен быть пустым массивом. \\nВАЖНО: Игнорируй любые другие ссылки кроме найденных в документах и указанных ниже:\\n{{$('Relevant_Links').first().json.relevant_links.map((item, i) => \\"   - [\\"+item.title + \\"](\\" + item.url+\\")\\").join('\\\\n')}}\\n\\n4. Выведи результат в точно такой структуре:\\n\\n{\\n  \\"data_from_knowladge_base\\": \\"основная информация, найденная по запросу пользователя в базе документов\\",\\n  \\"scraped_links_for_crawl\\": [\\n     \\"https://example.com/main-site-1\\",\\n     \\"https://example.com/main-site-2\\"\\n  ]\\n}\\n\\n- В поле \\"data_from_knowladge_base\\" укажи краткое, но полное текстовое объяснение/ответ по теме запроса, взятое из базы документов.\\n- В поле \\"scraped_links_for_crawl\\" укажи массив ссылок полученных из Scrapeless инструмента, которые нужно будет отправить на скрепинг. Тут должны быть релевантные ссылки к ресурсам, не страницы на глвную страницу, а конкретные к необходимому материалу\\n- Если релевантные ссылки не найдены, верни \\"scraped_links_for_crawl\\": []\\n\\nСледуй строго этому формату и требованиям.","maxIterations":10}},"type":"@n8n/n8n-nodes-langchain.agent","typeVersion":2.1,"position":[-336,0],"id":"9597592d-2e2f-4ab1-ac91-af493b5c10eb","name":"Agent_Collector"},{"parameters":{"mode":"retrieve-as-tool","toolName":"knowledge_base","toolDescription":"Use this knowledge base to answer questions from the user","memoryKey":{"__rl":true,"value":"documents_rag_store","mode":"list","cachedResultName":"documents_rag_store"},"topK":5},"type":"@n8n/n8n-nodes-langchain.vectorStoreInMemory","typeVersion":1.2,"position":[-96,288],"id":"14ace0bb-e41e-448c-bfb8-4d62f271af00","name":"Query_From_Documents"},{"parameters":{"mode":"insert","memoryKey":{"__rl":true,"value":"documents_rag_store","mode":"list"},"clearStore":true},"type":"@n8n/n8n-nodes-langchain.vectorStoreInMemory","typeVersion":1.2,"position":[-800,288],"id":"d0176a46-5cb3-448b-a7c3-4a67b98a8006","name":"Insert_Document_To_RAG"},{"parameters":{"jsonSchemaExample":"{\\n\\t\\"data_from_knowladge_base\\": \\"...\\",\\n\\t\\"scraped_links_for_crawl\\": [\\"link1\\", \\"link2\\"]\\n}","autoFix":true},"type":"@n8n/n8n-nodes-langchain.outputParserStructured","typeVersion":1.3,"position":[224,288],"id":"b43db475-0edc-4648-ba64-13331f344a82","name":"Structured Output Parser"},{"parameters":{"model":{"__rl":true,"value":"gpt-4.1-mini","mode":"list","cachedResultName":"gpt-4.1-mini"},"options":{"frequencyPenalty":0,"maxTokens":-1,"presencePenalty":0,"temperature":0,"timeout":60000,"maxRetries":2,"topP":1}},"type":"@n8n/n8n-nodes-langchain.lmChatOpenAi","typeVersion":1.2,"position":[592,1184],"id":"620ac612-25a1-4009-bda4-66e97d95a7a1","name":"OpenAI 4.1-mini","credentials":{"openAiApi":{"id":"peOYM0fm7X1sQZNB","name":"OpenAi account"}}},{"parameters":{"promptType":"define","text":"={{ $('Message_From_BCA_Agent').item.json.chatInput }}","hasOutputParser":true,"options":{"systemMessage":"=Ты специалист по извлечению данных. \\nТвои задачи:\\n1. Получи данные от предыдущего агента: {{ $('Agent_Collector').item.json.output }}\\n2. Используй инструмент Scrapeless для скрапинга каждой ссылки из \\"scraped_links_for_crawl\\"\\n3. Извлеки релевантную информацию по запросу пользователя\\n4. Верни структурированный JSON:\\n\\n{\\n  \\"scraped_data\\": [\\n    {\\n      \\"url\\": \\"ссылка\\", \\n      \\"title\\": \\"заголовок\\",\\n      \\"relevant_content\\": \\"извлеченный контент\\" \\n    } \\n  ] \\n}"}},"type":"@n8n/n8n-nodes-langchain.agent","typeVersion":2.1,"position":[880,-16],"id":"96a6a6a1-26a4-4c86-9d33-d560a9786b7b","name":"Agent Crawler"},{"parameters":{"conditions":{"options":{"caseSensitive":true,"leftValue":"","typeValidation":"loose","version":2},"conditions":[{"id":"96e456e9-97a5-4282-8493-4239d0312c75","leftValue":"={{ $json.output.scraped_links_for_crawl.length }}","rightValue":0,"operator":{"type":"number","operation":"gt"}}],"combinator":"and"},"looseTypeValidation":true,"options":{}},"type":"n8n-nodes-base.if","typeVersion":2.2,"position":[368,0],"id":"bfcc80fb-de49-4f95-b35f-8f63b94bab6a","name":"IF CRAWL REQUIRED"},{"parameters":{"promptType":"define","text":"={{ $('Message_From_BCA_Agent').item.json.chatInput }}","hasOutputParser":true,"options":{"systemMessage":"=Ты юридический аналитик по кейсам. \\nТвои задачи:\\n1. Проанализируй исходный запрос пользователя\\n2. Определи, является ли это кейсом (конкретная ситуация с описанием проблемы) или просто вопросом\\n3. Если это кейс (конкретная ситуация), обязательно вызови инструмент `Query_From_User_Cases` с текстом кейса, чтобы найти похожие прецеденты.\\n4. Вставь найденные кейсы в поле `similar_cases` в финальном JSON. ВАЖНО: если похожих кейсов нет - оставить поле пустым массивом.\\n5. Произведи поиск исправленных ответов на вопросы исходя из запроса пользователя вызвав инструмент `Query_From_Remarks` и получив исправленные ответы в поле `remarks`, указав вопрос и правильный ответ. Не изменяй исправленный ответ полученный из базы знаний, используй только оригинал.\\n6. Верни структурированный JSON:\\n\\n{\\n  \\"is_case\\": true/false, \\n  \\"case_analysis\\": \\"анализ текущей ситуации\\",\\n  \\"similar_cases\\": [{\\"title\\": \\"название выгруженного кейса\\", \\"issue\\": \\"полное описание проблемы выгруженного кейса\\", \\"result\\": \\"полное описание того как выгруженный кейс был решен\\"}], //массив названий похожих кейсов если найдены\\n  \\"case_summary\\": \\"краткое описание текущего кейса\\",\\n  \\"remarks\\": [\\n    {\\n      \\"question\\": \\"[исходный вопрос из документа]\\",\\n      \\"answer\\": \\"[оригинальный исправленный полный ответ на вопрос]\\"\\n    }\\n  ]\\n}"}},"type":"@n8n/n8n-nodes-langchain.agent","typeVersion":2.1,"position":[1376,480],"id":"43f9eb9b-9273-4990-8c96-910bce387265","name":"Case_Analyzer_Agent","executeOnce":true},{"parameters":{"formTitle":"Upload your data to test RAG","formFields":{"values":[{"fieldLabel":"Upload your file(s)","fieldType":"file","acceptFileTypes":".pdf, .csv","requiredField":true}]},"options":{}},"type":"n8n-nodes-base.formTrigger","typeVersion":2.2,"position":[-1024,800],"id":"f9630f97-6eaa-40be-97fe-f93ce42892ec","name":"Upload User-Cases","webhookId":"17a86d3f-d7cc-46e2-a52c-34b3a0b5c7fc"},{"parameters":{"mode":"insert","memoryKey":{"__rl":true,"value":"user_cases_rag_store","mode":"list"},"clearStore":true},"type":"@n8n/n8n-nodes-langchain.vectorStoreInMemory","typeVersion":1.2,"position":[-816,800],"id":"a918e641-da74-4c32-855b-d0268c8b3eee","name":"Insert_User_Case_To_RAG"},{"parameters":{"dataType":"binary","options":{}},"type":"@n8n/n8n-nodes-langchain.documentDefaultDataLoader","typeVersion":1.1,"position":[-1504,1088],"id":"819c3e6f-564e-4c7d-a2c5-2e17b76fe334","name":"PDF, CSV File Uploader"},{"parameters":{"mode":"retrieve-as-tool","toolName":"Query_From_User_Cases","toolDescription":"База юзер-кейсов с уникальными случаями разрешенные в компании","memoryKey":{"__rl":true,"value":"user_cases_rag_store","mode":"list","cachedResultName":"user_cases_rag_store"},"topK":5},"type":"@n8n/n8n-nodes-langchain.vectorStoreInMemory","typeVersion":1.2,"position":[1168,704],"id":"b4afcb53-950b-4b75-a456-4b5b9c7815d3","name":"Query_From_User_Cases"},{"parameters":{"options":{}},"type":"@n8n/n8n-nodes-langchain.embeddingsOpenAi","typeVersion":1.2,"position":[-80,1184],"id":"d09ccad6-7af2-4c0b-a237-c411635946c0","name":"Embeddings OpenAI 3-small","credentials":{"openAiApi":{"id":"peOYM0fm7X1sQZNB","name":"OpenAi account"}}},{"parameters":{"formTitle":"Upload your data to test RAG","formFields":{"values":[{"fieldLabel":"Upload your file(s)","fieldType":"file","acceptFileTypes":".pdf, .csv","requiredField":true}]},"options":{}},"type":"n8n-nodes-base.formTrigger","typeVersion":2.2,"position":[-1024,1232],"id":"4c03496d-ba5e-42ff-aee4-311f45775bd8","name":"Upload Answers Remarks","webhookId":"d9c0d2bd-1540-4cd7-aa4d-2c89234df3be"},{"parameters":{"mode":"insert","memoryKey":{"__rl":true,"value":"remarks_store","mode":"list"},"clearStore":true},"type":"@n8n/n8n-nodes-langchain.vectorStoreInMemory","typeVersion":1.2,"position":[-816,1232],"id":"3b9066a1-5bb8-491d-a03f-7a4a759fca6c","name":"Insert_Remarks_To_RAG"},{"parameters":{"promptType":"=define","text":"={{ $json.output }}","hasOutputParser":true,"options":{"systemMessage":"=Ты — главнй юридический консультант. Получаешь агрегированный входной объект, собранный работы нескольких агентов.\\n\\nВот структура входных данных:\\n- **case_summary** — краткое описание исходной ситуации пользователя.\\n- **similar_cases** — массив похожих кейсов, найденных предыдущим агентом (если таких кейсов нет — массив пустой).\\n- **data_from_knowledge_base** — релевантная информация из RAG/базы знаний.\\n- **scraped_data** — результаты внешнего скрапинга, если он был выполнен.\\n- **chatInput** — исходный вопрос/описание пользователя.\\n- **is_case** - является ли входящий запрос пользователя кейсом\\n\\n\\n## Твои задачи:\\n1. Проанализируй case_summary и user_query — это суть запроса пользователя.\\n2. Всегда используй similar_cases, если есть — на их основе делай финальный вывод, когда применимо.\\n3. Обязательно подключай релевантную информацию из базы (data_from_knowledge_base) и/или scraped_data как аргументы/примеры в анализе, если они есть.\\n4. Используй инструмент поиска правок по вопросам-ответам\\n5. Примени найденные правки к ответу если требуется\\n6. Формируй финальный ответ в ЧЕТКОЙ JSON-структуре:\\n\\n```json\\n{\\n  \\"final_verdict\\": \\"Финальная рекомендация, выжимка и анализ ситуации с учетом всех источников информации.\\",\\n  \\"reasons\\": \\"Аргументы и объяснения, почему был сделан такой вывод. Укажи, какие кейсы/документы/данные были использованы.\\",\\n  \\"used_similar_cases\\": [ /* массив кейсов из similar_cases, если они были найдены, иначе [] */ ],\\n  \\"full_context\\": {\\n    \\"case_summary\\": \\"...\\",\\n    \\"user_query\\": \\"входной запрос пользователя\\",\\n    \\"data_from_knowledge_base\\": \\"...\\",\\n    \\"scraped_data\\": \\"...\\"\\n  }\\n}\\nВАЖНО:\\n\\nЕсли similar_cases пуст — оставь used_similar_cases как [] (пустой массив).\\nВсегда ссылайся в аргументации на те источники и поля, которые реально доступны в объекте.\\nНе выдумывай информацию — используй только те данные, которые пришли от предыдущих агентов или пользовательский input."}},"type":"@n8n/n8n-nodes-langchain.agent","typeVersion":2.1,"position":[2416,832],"id":"405381c8-e87f-4a6b-8d52-96702060d8e7","name":"Final Agent"},{"parameters":{"sessionIdType":"customKey","sessionKey":"={{ $('Message_From_BCA_Agent').first().json.sessionId }}"},"type":"@n8n/n8n-nodes-langchain.memoryBufferWindow","typeVersion":1.3,"position":[224,1168],"id":"4de60af1-6658-4878-9bc6-afb7d086e7f9","name":"Shared_Agents_Chat_Context_Memory"},{"parameters":{"formTitle":"Upload your data to test RAG","formFields":{"values":[{"fieldLabel":"Upload your file(s)","fieldType":"file","acceptFileTypes":".pdf, .csv","requiredField":true}]},"options":{}},"type":"n8n-nodes-base.formTrigger","typeVersion":2.2,"position":[-1040,288],"id":"e9e45549-47b4-44da-a88b-c4bba45383a3","name":"Upload Documents","webhookId":"b19d2e01-6e8c-422e-916a-5a28210a6844"},{"parameters":{"q":"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Search_Query', ``, 'string') }}","hl":"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Language', ``, 'string') }}","gl":"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Country', ``, 'string') }}","location":"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Location', ``, 'string') }}","start":"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Result_Offset', ``, 'number') }}","num":"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Number_of_Results', ``, 'number') }}","ludocid":"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Google_Place_ID', ``, 'string') }}","kgmid":"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Google_Knowledge_Graph_ID', ``, 'string') }}","ibp":"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Google_Element_Rendering', ``, 'string') }}"},"type":"n8n-nodes-scrapeless.scrapelessTool","typeVersion":1,"position":[-208,288],"id":"b5f3c21c-88e9-487a-8b2e-f911ebedee96","name":"Crawl","credentials":{"scrapelessApi":{"id":"oIC7E4xcxOQfQXRZ","name":"Scrapeless account"}},"onError":"continueRegularOutput"},{"parameters":{"resource":"crawler","url":"={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('URL_to_Crawl', ``, 'string') }}"},"type":"n8n-nodes-scrapeless.scrapelessTool","typeVersion":1,"position":[1008,240],"id":"b4f04575-5e25-4361-bf96-5bfd5a6b3079","name":"Scrape","credentials":{"scrapelessApi":{"id":"oIC7E4xcxOQfQXRZ","name":"Scrapeless account"}}},{"parameters":{"jsonSchemaExample":"{\\n\\n  \\"scraped_data\\": [\\n    {\\n      \\"url\\": \\"ссылка\\", \\n      \\"title\\": \\"заголовок\\",\\n      \\"relevant_content\\": \\"извлеченный контент\\" \\n    } \\n  ] \\n}"},"type":"@n8n/n8n-nodes-langchain.outputParserStructured","typeVersion":1.3,"position":[1120,240],"id":"dbbbb936-7cd4-4ec5-9e66-f0aae787ffb6","name":"Structured Output Parser1"},{"parameters":{"jsonSchemaExample":"{\\n  \\"is_case\\": false,\\n  \\"case_analysis\\": \\"[анализ текущей ситуации]\\",\\n  \\"similar_cases\\": [\\n    {\\n      \\"title\\": \\"[Номер выгруженного кейса] - [краткое наименование выгруженного кейса] ([имя клиента из выгруженного кейса])\\",\\n      \\"issues\\": \\"[полное описание проблемы с которой столкнулся клиент в выгруженном кейсе]\\",\\n      \\"result\\": \\"[полное описание как была решена проблема клиента из выгруженного кейса]\\"\\n    }\\n  ],\\n  \\"case_summary\\": \\"[краткое описание текущего кейса]\\",\\n  \\"remarks\\": [\\n    {\\n      \\"question\\": \\"[исходный вопрос из документа]\\",\\n      \\"answer\\": \\"[исходный исправленный полный ответ на вопрос]\\"\\n    }\\n  ]\\n}","autoFix":true},"type":"@n8n/n8n-nodes-langchain.outputParserStructured","typeVersion":1.3,"position":[1776,704],"id":"29c129fd-1f7d-4d6f-821f-7bd4ef638f8b","name":"Structured Output Parser2"},{"parameters":{"jsonSchemaExample":"{\\n  \\"used_documents\\": [\\"цитаты из документов\\"], \\n  \\"used_case\\": \\"описание использованного кейса или null\\", \\n  \\"used_resources\\": [\\n    {\\n      \\"url\\": \\"ссылка\\", \\n      \\"title\\": \\"заголовок\\",\\n      \\"fragment\\": \\"использованный фрагмент\\"\\n    }\\n  ],\\n  \\"final_answer\\": \\"полный развернутый ответ на запрос с учетом всех данных и правок\\"\\n}","autoFix":true},"type":"@n8n/n8n-nodes-langchain.outputParserStructured","typeVersion":1.3,"position":[2624,1264],"id":"74fb6e30-ceae-4d6f-9dc3-3814ff6243e0","name":"Structured Output Parser3"},{"parameters":{"jsCode":"\\nconst $analyzerOutput = $(\\"Case_Analyzer_Agent\\").first().json.output;\\nconst $collector = $(\\"Agent_Collector\\").first().json.output;\\nconst $crawler = $collector.scraped_links_for_crawl.length > 0 ? $(\\"Agent Crawler\\").first().json.output : {\\"scraped_data\\" : []};\\nconst $userQuery = {\\"user_query\\": $(\\"Message_From_BCA_Agent\\").first().json.chatInput}\\n\\nreturn {...$analyzerOutput, ...$collector, ...$crawler, ...$userQuery};"},"type":"n8n-nodes-base.code","typeVersion":2,"position":[1728,480],"id":"00f0f291-8fa9-4768-a26f-7698225ca577","name":"Code"},{"parameters":{"mode":"retrieve-as-tool","toolName":"knowledge_base","toolDescription":"Use this knowledge base to answer questions from the user","memoryKey":{"__rl":true,"value":"remarks_store","mode":"list","cachedResultName":"remarks_store"},"topK":5},"type":"@n8n/n8n-nodes-langchain.vectorStoreInMemory","typeVersion":1.2,"position":[2320,1264],"id":"7bb5edeb-689e-41f4-8705-e32770ae374c","name":"Query_From_Remarks_Old"},{"parameters":{"mode":"retrieve-as-tool","toolName":"Query_From_Remarks","toolDescription":"База знаний которая содержи в себе исправленные юридические вопросы с ответами на них","memoryKey":{"__rl":true,"value":"remarks_store","mode":"list","cachedResultName":"remarks_store"},"topK":5},"type":"@n8n/n8n-nodes-langchain.vectorStoreInMemory","typeVersion":1.2,"position":[1472,704],"id":"7829f073-07c4-46e5-913e-400544afa1e3","name":"Query_From_Remarks"},{"parameters":{"model":{"__rl":true,"value":"gpt-4o","mode":"list","cachedResultName":"gpt-4o"},"options":{"temperature":0}},"type":"@n8n/n8n-nodes-langchain.lmChatOpenAi","typeVersion":1.2,"position":[1040,704],"id":"94e87f75-0d00-4064-851f-bdba620c66c6","name":"OpenAI 4o","credentials":{"openAiApi":{"id":"peOYM0fm7X1sQZNB","name":"OpenAi account"}}}],"connections":{"Message_From_BCA_Agent":{"main":[[{"node":"Relevant_Links","type":"main","index":0}]]},"Relevant_Links":{"main":[[{"node":"Agent_Collector","type":"main","index":0}]]},"Query_From_Documents":{"ai_tool":[[{"node":"Agent_Collector","type":"ai_tool","index":0}]]},"Structured Output Parser":{"ai_outputParser":[[{"node":"Agent_Collector","type":"ai_outputParser","index":0}]]},"OpenAI 4.1-mini":{"ai_languageModel":[[{"node":"Agent_Collector","type":"ai_languageModel","index":0},{"node":"Agent Crawler","type":"ai_languageModel","index":0},{"node":"Structured Output Parser","type":"ai_languageModel","index":0},{"node":"Final Agent","type":"ai_languageModel","index":0},{"node":"Structured Output Parser2","type":"ai_languageModel","index":0},{"node":"Structured Output Parser3","type":"ai_languageModel","index":0}]]},"Agent_Collector":{"main":[[{"node":"IF CRAWL REQUIRED","type":"main","index":0}]]},"IF CRAWL REQUIRED":{"main":[[{"node":"Agent Crawler","type":"main","index":0}],[{"node":"Case_Analyzer_Agent","type":"main","index":0}]]},"Upload User-Cases":{"main":[[{"node":"Insert_User_Case_To_RAG","type":"main","index":0}]]},"PDF, CSV File Uploader":{"ai_document":[[{"node":"Insert_User_Case_To_RAG","type":"ai_document","index":0},{"node":"Insert_Document_To_RAG","type":"ai_document","index":0},{"node":"Insert_Remarks_To_RAG","type":"ai_document","index":0}]]},"Query_From_User_Cases":{"ai_tool":[[{"node":"Case_Analyzer_Agent","type":"ai_tool","index":0}]]},"Embeddings OpenAI 3-small":{"ai_embedding":[[{"node":"Insert_Document_To_RAG","type":"ai_embedding","index":0},{"node":"Query_From_Documents","type":"ai_embedding","index":0},{"node":"Query_From_User_Cases","type":"ai_embedding","index":0},{"node":"Insert_User_Case_To_RAG","type":"ai_embedding","index":0},{"node":"Insert_Remarks_To_RAG","type":"ai_embedding","index":0},{"node":"Query_From_Remarks_Old","type":"ai_embedding","index":0},{"node":"Query_From_Remarks","type":"ai_embedding","index":0}]]},"Upload Answers Remarks":{"main":[[{"node":"Insert_Remarks_To_RAG","type":"main","index":0}]]},"Case_Analyzer_Agent":{"main":[[{"node":"Code","type":"main","index":0}]]},"Agent Crawler":{"main":[[{"node":"Case_Analyzer_Agent","type":"main","index":0}]]},"Shared_Agents_Chat_Context_Memory":{"ai_memory":[[{"node":"Agent_Collector","type":"ai_memory","index":0},{"node":"Agent Crawler","type":"ai_memory","index":0},{"node":"Case_Analyzer_Agent","type":"ai_memory","index":0},{"node":"Final Agent","type":"ai_memory","index":0}]]},"Upload Documents":{"main":[[{"node":"Insert_Document_To_RAG","type":"main","index":0}]]},"Crawl":{"ai_tool":[[{"node":"Agent_Collector","type":"ai_tool","index":0}]]},"Scrape":{"ai_tool":[[{"node":"Agent Crawler","type":"ai_tool","index":0}]]},"Structured Output Parser1":{"ai_outputParser":[[{"node":"Agent Crawler","type":"ai_outputParser","index":0}]]},"Structured Output Parser2":{"ai_outputParser":[[{"node":"Case_Analyzer_Agent","type":"ai_outputParser","index":0}]]},"Structured Output Parser3":{"ai_outputParser":[[{"node":"Final Agent","type":"ai_outputParser","index":0}]]},"Final Agent":{"main":[[]]},"Code":{"main":[[]]},"Query_From_Remarks_Old":{"ai_tool":[[{"node":"Final Agent","type":"ai_tool","index":0}]]},"Query_From_Remarks":{"ai_tool":[[{"node":"Case_Analyzer_Agent","type":"ai_tool","index":0}]]},"OpenAI 4o":{"ai_languageModel":[[{"node":"Case_Analyzer_Agent","type":"ai_languageModel","index":0}]]}},"createdAt":"2025-07-23T19:55:24.924+00:00","updatedAt":"2025-07-25T14:17:47.115+00:00","settings":{"executionOrder":"v1"},"staticData":null,"pinData":{},"versionId":"9f962ac1-9dc7-41fe-a90c-e95a54a2ffd8","triggerCount":4,"id":"scaGX3kA2XBJbeAf","meta":{"templateCredsSetupCompleted":true},"parentFolderId":null,"isArchived":false}]
