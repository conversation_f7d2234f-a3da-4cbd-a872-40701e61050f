import { Redis } from '@upstash/redis';

// Мок-хранилище для локальной разработки
const mockStorage = new Map<string, any>();

// Мок-объект Redis для локальной разработки
const mockRedis = {
  async get(key: string) {
    return mockStorage.get(key) || null;
  },
  async set(key: string, value: any) {
    mockStorage.set(key, value);
    return 'OK';
  },
  async del(key: string) {
    const existed = mockStorage.has(key);
    mockStorage.delete(key);
    return existed ? 1 : 0;
  },
  async keys(pattern: string) {
    const keys = Array.from(mockStorage.keys());
    if (pattern === '*') return keys;
    // Простая поддержка паттернов с *
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return keys.filter(key => regex.test(key));
  },
  async mget(...keys: string[]) {
    return keys.map(key => mockStorage.get(key) || null);
  }
};

// Проверяем, являются ли URL валидными для Upstash
const isValidUpstashUrl = (url: string | undefined) => {
  return url && url.startsWith('https://') && url.includes('upstash.io');
};

// Используем реальный Redis если URL валидный, иначе мок
export const redis = isValidUpstashUrl(process.env.KV_REST_API_URL)
  ? new Redis({
      url: process.env.KV_REST_API_URL,
      token: process.env.KV_REST_API_TOKEN
    })
  : mockRedis;
